from pydantic import create_model, BaseModel
from autogen_core.tools import BaseTool, FunctionTool
from autogen_core import CancellationToken

import asyncio
import requests
import logging
from typing import Dict, List, Any, Optional
from typing_extensions import Annotated
from ..shared.config.base import get_settings
from ..helper.api_call import HttpRequestHelper, AuthType
from ..services.knowledge_service import KnowledgeSseClientWrapper

# Configure logger
logger = logging.getLogger(__name__)

# Map JSON Schema types to Python types
json_type_to_py = {
    "string": str,
    "integer": int,
    "number": float,
    "boolean": bool,
    "object": dict,
    "array": list,
}

knowledge_execute_endpoint = "knowledge-execute/server"
stream_endpoint = "knowledge-execute/stream"


class KnowledgeToolLoader:
    """
    Utility class to load knowledges as dynamic tools and execute them.
    """

    def __init__(self):
        """
        Initialize the knowledge tool loader.

        Args:
            knowledge_api_base_url: Base URL for knowledge execution API
            stream_api_base_url: Base URL for streaming results
            auth_token: Optional authentication token
            auth_type: Authentication type (default: BEARER)
        """
        self.settings = get_settings()
        logger.info("Initializing KnowledgeToolLoader")

        # Initialize HTTP request helper for API calls
        self.http_client = HttpRequestHelper(
            base_url=self.settings.workflow_api_gateway.api_url,
            auth_token=self.settings.workflow_api_gateway.api_key,
            auth_type=AuthType.API_KEY,
            api_key_name="X-Agent-Platform-Auth-Key",
            timeout=60,  # Longer timeout for knowledge operations
        )
        self.knowledge_client = KnowledgeSseClientWrapper()
        logger.debug(
            f"HTTP client initialized with base URL: {self.settings.workflow_api_gateway.api_url}"
        )

    async def create_knowledge_tool_from_metadata(
        self, knowledge_metadata: Dict[str, Any]
    ) -> BaseTool:
        """
        Create a dynamic tool from knowledge metadata.

        Args:
            knowledge_metadata: Knowledge metadata from the API

        Returns:
            A BaseTool instance that can execute the knowledge
        """

        url = knowledge_metadata.get("url")

        tools = await self.knowledge_client.connect_to_server(
            url=knowledge_metadata.get("url"),
            headers=None,
        )

        print(f"Tools: {tools}")

        knowledge_tools = []

        for tool in tools:
            name = tool.name
            description = tool.description
            json_schema = tool.inputSchema

            logger.info(f"Knowledge tool schema for {name}: {json_schema}")

            # Create the dynamic tool with our execution endpoint
            knowledge_tools.append(
                self._create_knowledge_execution_tool(
                    url=url,
                    name=name,
                    description=description,
                    json_schema=json_schema,
                )
            )

        return knowledge_tools

    def _create_knowledge_execution_tool(
        self, url: str, name: str, description: str, json_schema: Dict[str, Any]
    ) -> BaseTool:
        """
        Create a dynamic tool that executes a knowledge.

        Args:
            url: url of the knowledge to execute
            name: Name for the tool
            description: Description for the tool
            json_schema: JSON schema for the tool parameters

        Returns:
            A BaseTool instance
        """
        logger.debug(f"Creating execution tool for knowledge {url} with name {name}")

        # Build fields dict with correct Python types
        fields = {}
        for k, v in json_schema.get("properties", {}).items():
            py_type = json_type_to_py.get(v.get("type", "string"), str)
            default = ... if k in json_schema.get("required", []) else None
            fields[k] = (py_type, default)
            logger.debug(f"Added field {k} with type {py_type} to tool schema")

        ArgsModel = create_model(f"{name}_Args", **fields)
        ArgsModel.model_rebuild()  # Ensure the model is fully defined
        logger.debug(f"Created args model for {name}: {ArgsModel}")

        class KnowledgeTool(BaseTool[ArgsModel, BaseModel]):
            def __init__(self, loader: KnowledgeToolLoader):
                super().__init__(
                    args_type=ArgsModel,
                    return_type=BaseModel,
                    name=name,
                    description=description,
                )
                self.url = url
                self.loader = loader
                logger.debug(f"Initialized KnowledgeTool for {url}")

            async def run(self, args: ArgsModel, cancellation_token: CancellationToken):
                """
                Execute the knowledge and return the result as a string.
                If the knowledge returns a stream, join the stream and return the full result.
                If an error occurs, return a user-friendly error message.
                """
                args_dict = args.model_dump(exclude_none=True)

                payload = {
                    "server_script_path": self.url,
                    "tool_name": name,
                    "tool_parameters": args_dict,
                    "retries": 3,
                }
                logger.debug(f"Knowledge execution payload: {payload}")

                try:
                    logger.info(f"Sending execution request for knowledge {self.url}")
                    response = await asyncio.to_thread(
                        self.loader.http_client.post,
                        knowledge_execute_endpoint,
                        json_data=payload,
                    )
                    logger.debug(f"Execution response: {response}")

                    if isinstance(response, dict) and "RequestId" in response:
                        request_id = response["RequestId"]
                        logger.info(f"Got request ID from response: {request_id}")
                    else:
                        logger.warning(f"No request ID in response: {response}")

                    # # Stream the results
                    # async def stream_results():
                    #     stream_url = f"{stream_endpoint}/{request_id}"
                    #     logger.info(f"Starting to stream results from: {stream_url}")

                    #     async with httpx.AsyncClient(
                    #         base_url=self.loader.http_client.base_url,
                    #         headers=self.loader.http_client.headers,
                    #         timeout=300,
                    #     ) as client:
                    #         try:
                    #             logger.info(
                    #                 f"Opening stream connection to {stream_url}"
                    #             )
                    #             async with client.stream(
                    #                 HttpMethods.GET.value, stream_url
                    #             ) as response:
                    #                 response.raise_for_status()
                    #                 logger.info(
                    #                     f"Stream connection established, status: {response.status_code}"
                    #                 )

                    #                 buffer = ""
                    #                 async for chunk in response.aiter_text():
                    #                     logger.info(
                    #                         f"Received stream chunk of size: {len(chunk)} bytes"
                    #                     )
                    #                     buffer += chunk
                    #                     while "\n\n" in buffer:
                    #                         message, buffer = buffer.split("\n\n", 1)
                    #                         for line in message.split("\n"):
                    #                             if line.startswith("data:"):
                    #                                 data = line[5:].strip()
                    #                                 try:
                    #                                     json_data = json.loads(data)
                    #                                     logger.info(
                    #                                         f"Parsed JSON data from stream: {json_data}"
                    #                                     )
                    #                                     yield json.dumps(json_data)
                    #                                 except json.JSONDecodeError:
                    #                                     logger.warning(
                    #                                         f"Failed to parse JSON from stream: {data}"
                    #                                     )
                    #                                     yield data
                    #         except Exception as e:
                    #             logger.error(
                    #                 f"Error streaming results: {str(e)}", exc_info=True
                    #             )
                    #             yield json.dumps(
                    #                 {"error": f"Streaming error: {str(e)}"}
                    #             )

                    # # Join the stream and return the full result as a string
                    # logger.info(f"Returning stream generator for knowledge {self.url}")
                    # result_chunks = []
                    # async for chunk in stream_results():
                    #     result_chunks.append(chunk)
                    # return "\n".join(result_chunks)

                    return {
                        "success": True,
                        "message": f"{name} Knowledge execution initiated",
                        "correlation_id": request_id,
                    }

                except Exception as e:
                    logger.error(
                        f"Error executing knowledge {self.url}: {str(e)}",
                        exc_info=True,
                    )
                    return f"Knowledge execution error: {str(e)}"

            def return_value_as_string(self, value):
                return str(value)

            async def join_stream(self, value):
                """
                Helper to join all chunks from an async generator.
                If value is already a string, return as is.
                """
                logger.info(f"Joining stream for knowledge {self.url}")
                if hasattr(value, "__aiter__"):
                    results = []
                    async for chunk in value:
                        logger.info(f"Appending chunk of size: {len(chunk)} bytes")
                        results.append(chunk)
                    joined = "\n".join(results)
                    logger.info(f"Joined stream, total size: {len(joined)} bytes")
                    return joined
                logger.info("Value is not a stream, returning as string")
                return str(value)

        logger.info(f"Created knowledge tool: {name}")
        return KnowledgeTool(self)

    async def load_knowledges_as_tools(self, knowledges) -> List[BaseTool]:
        """
        Load all available knowledges as tools.

        Args:
            knowledges_endpoint: API endpoint to get knowledge metadata

        Returns:
            List of BaseTool instances for each knowledge
        """
        try:
            logger.info(
                f"Loading knowledges as tools, count: {len(knowledges) if isinstance(knowledges, list) else 'unknown'}"
            )

            if not isinstance(knowledges, list):
                logger.error(
                    f"Error: Expected list of knowledges, got {type(knowledges)}"
                )
                return []

            # Create a tool for each knowledge
            tools = []
            for knowledge in knowledges:
                try:
                    knowledge_id = knowledge.get("id", "unknown")
                    logger.info(f"Creating tool for knowledge: {knowledge_id}")
                    knowledge_tools = await self.create_knowledge_tool_from_metadata(
                        knowledge
                    )
                    tools.extend(knowledge_tools)
                    logger.info(
                        f"Successfully created tool for knowledge: {knowledge_id}"
                    )
                except Exception as e:
                    logger.error(
                        f"Error creating tool for knowledge {knowledge.get('id', 'unknown')}: {str(e)}",
                        exc_info=True,
                    )

            logger.info(f"Successfully loaded {len(tools)} knowledge tools")
            return tools

        except Exception as e:
            logger.error(f"Error loading knowledges: {str(e)}", exc_info=True)
            return []

    def create_knowledge_content_tools(
        self,
        organization_id: Optional[str] = None,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
    ) -> List[FunctionTool]:
        """
        Create knowledge content retrieval tools as FunctionTool instances.

        Args:
            organization_id: Optional organization ID for scoped knowledge access

        Returns:
            List of FunctionTool instances for knowledge content retrieval
        """
        tools = []

        # Create get_knowledge_content tool
        async def get_knowledge_content(
            query: Annotated[str, "Search query to find relevant knowledge content"],
            top_k: Annotated[int, "Maximum number of results to return"] = 5,
        ) -> str:
            """
            Search and retrieve knowledge content from Google Drive based on a query.

            This function searches through the organization's Google Drive knowledge base
            and returns relevant content that matches the query.
            """
            try:

                # Prepare search payload for Google Drive API
                search_payload = {
                    "user_id": user_id,
                    "query_text": query,
                    "top_k": top_k,
                    "agent_id": agent_id,
                    "organisation_id": organization_id,
                }

                logger.info(f"Searching Google Drive knowledge with query: {query}")

                # Make API call to Google Drive search endpoint
                url = f"{self.settings.workflow_api_gateway.api_url}/api/v1/google-drive/search"

                headers = {
                    "Content-Type": "application/json",
                    "X-Org-Auth-Key": self.settings.workflow_api_gateway.api_key,
                }

                response = await asyncio.to_thread(
                    requests.post,
                    url,
                    json=search_payload,
                    headers=headers,
                    timeout=30,
                )

                logger.info(f"Google Drive API response status: {response.status_code}")

                if response.status_code == 200:
                    response_data = response.json()

                    # Check if we have results
                    if "results" in response_data and response_data["results"]:
                        results = response_data["results"]

                        # Format results
                        formatted_results = []
                        for i, result in enumerate(results[:top_k], 1):
                            file_name = result.get(
                                "file_name", result.get("name", "Untitled")
                            )
                            content = result.get(
                                "content",
                                result.get("chunk_text", "No content available"),
                            )
                            source = result.get(
                                "source", result.get("web_view_link", "Google Drive")
                            )
                            mime_type = result.get("mime_type", "Unknown")
                            score = result.get("score", "N/A")

                            formatted_results.append(
                                f"{i}. **{file_name}**\n"
                                f"   mime_type: {mime_type}\n"
                                f"   Source: {source}\n"
                                f"   Relevance Score: {score}\n"
                                f"   Content: {content}\n"
                            )

                        return "\n".join(formatted_results)
                    else:
                        return f"No knowledge content found for query: {query}"
                else:
                    error_msg = f"API request failed with status {response.status_code}"
                    try:
                        error_data = response.json()
                        if "error" in error_data:
                            error_msg += f": {error_data['error']}"
                    except:
                        error_msg += f": {response.text}"
                    return f"Error searching knowledge: {error_msg}"

            except Exception as e:
                logger.error(f"Error in get_knowledge_content: {str(e)}", exc_info=True)
                return f"Error retrieving knowledge content: {str(e)}"

        # Create get_batch_knowledge_content tool
        async def get_batch_knowledge_content(
            query_texts: Annotated[
                List[str], "List of search queries to find relevant knowledge content"
            ],
            user_id: Annotated[str, "User ID for the search request"],
            agent_id: Annotated[str, "Agent ID for the search request"],
            top_k: Annotated[int, "Maximum number of results to return per query"] = 5,
        ) -> str:
            """
            Search multiple queries in Google Drive knowledge base using batch search.

            This function performs batch search across multiple queries and returns
            relevant content for each query from the organization's Google Drive.
            """
            try:
                import requests

                # Prepare batch search payload for Google Drive API
                batch_payload = {
                    "user_id": user_id,
                    "query_texts": query_texts,
                    "top_k": top_k,
                    "agent_id": agent_id,
                    "organisation_id": organization_id,
                }

                logger.info(f"Performing batch search with {len(query_texts)} queries")

                # Make API call to Google Drive batch search endpoint
                url = f"{self.settings.workflow_api_gateway.api_url}/api/v1/google-drive/batch-search"
                headers = {
                    "Content-Type": "application/json",
                    "X-Agent-Platform-Auth-Key": self.settings.workflow_api_gateway.api_key,
                }

                response = await asyncio.to_thread(
                    requests.post,
                    url,
                    json=batch_payload,
                    headers=headers,
                    timeout=60,  # Longer timeout for batch operations
                )

                logger.info(
                    f"Google Drive batch API response status: {response.status_code}"
                )

                if response.status_code == 200:
                    response_data = response.json()

                    # Check if we have results
                    if "results" in response_data and response_data["results"]:
                        batch_results = response_data["results"]

                        # Format batch results
                        formatted_sections = []

                        for query_idx, query_result in enumerate(batch_results):
                            query_text = (
                                query_texts[query_idx]
                                if query_idx < len(query_texts)
                                else f"Query {query_idx + 1}"
                            )

                            formatted_sections.append(f"## Results for: '{query_text}'")

                            if "results" in query_result and query_result["results"]:
                                results = query_result["results"]

                                for i, result in enumerate(results[:top_k], 1):
                                    title = result.get(
                                        "title", result.get("name", "Untitled")
                                    )
                                    content = result.get(
                                        "content",
                                        result.get("snippet", "No content available"),
                                    )
                                    source = result.get(
                                        "source",
                                        result.get("file_path", "Google Drive"),
                                    )
                                    file_id = result.get("file_id", "Unknown")
                                    score = result.get("score", "N/A")

                                    formatted_sections.append(
                                        f"{i}. **{title}**\n"
                                        f"   File ID: {file_id}\n"
                                        f"   Source: {source}\n"
                                        f"   Relevance Score: {score}\n"
                                        f"   Content: {content[:150]}{'...' if len(content) > 150 else ''}"
                                    )
                            else:
                                formatted_sections.append(
                                    "   No results found for this query."
                                )

                            formatted_sections.append("")  # Add spacing between queries

                        return "\n".join(formatted_sections)
                    else:
                        return "No results found for any of the queries."
                else:
                    error_msg = (
                        f"Batch API request failed with status {response.status_code}"
                    )
                    try:
                        error_data = response.json()
                        if "error" in error_data:
                            error_msg += f": {error_data['error']}"
                    except Exception:
                        error_msg += f": {response.text}"
                    return f"Error performing batch search: {error_msg}"

            except Exception as e:
                logger.error(
                    f"Error in get_batch_knowledge_content: {str(e)}", exc_info=True
                )
                return f"Error retrieving batch knowledge content: {str(e)}"

        # Create FunctionTool instances
        tools.append(
            FunctionTool(
                get_knowledge_content,
                description="Search and retrieve knowledge content based on a query. "
                "Use this tool to find relevant information from the knowledge base.",
            )
        )

        # tools.append(
        #     FunctionTool(
        #         get_batch_knowledge_content,
        #         description="Perform batch search across multiple queries in Google Drive knowledge base. "
        #         "Use this tool when you need to search for multiple topics simultaneously.",
        #     )
        # )

        logger.info(f"Created {len(tools)} knowledge content tools")
        return tools
