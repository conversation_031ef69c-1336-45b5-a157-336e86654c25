import json
import logging
from typing import Any, Dict, List, Optional  # Import Optional
from aiokafka import AIOKafkaProducer  # type: ignore

from ..shared.config.base import get_settings


# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class KafkaProducer:

    def __init__(self) -> None:
        self.logger = logger
        self.settings = get_settings()
        self.kafka_broker: str = self.settings.kafka.kafka_bootstrap_servers
        # Initialize producer to None. It will be created in start_producer
        self.producer: Optional[AIOKafkaProducer] = None
        self.logger.info(
            "Kafka producer class initialized (producer object not created yet)"
        )

    async def start_producer(self) -> None:
        # Check if producer already exists (optional, but good practice)
        if self.producer is not None:
            self.logger.warning("Producer already started.")
            return

        try:
            # Create the AIOKafkaProducer instance here, inside the async method
            self.producer = AIOKafkaProducer(
                bootstrap_servers=self.kafka_broker,
                max_request_size=524288000,  # Consider if this large size is truly needed
                value_serializer=lambda v: json.dumps(v).encode("utf-8"),
                # loop=asyncio.get_running_loop() # Usually not needed anymore, aiokafka finds it
            )
            # Start the producer
            await self.producer.start()
            self.logger.info("Kafka producer created and started successfully")
        except Exception as e:
            self.logger.error(f"Failed to create or start Kafka producer: {e}")
            # Optional: re-raise the exception if startup failure should stop the app
            # raise

    async def stop_producer(self) -> None:
        if self.producer:
            await self.producer.stop()
            self.producer = None  # Clear the reference
            self.logger.info("Kafka producer stopped")
        else:
            self.logger.warning(
                "Attempted to stop a producer that was not running or initialized."
            )

    async def send_message(
        self,
        topic: str,
        message: Dict[str, Any],
        headers: List[tuple[str, bytes]] = None,
    ) -> None:

        print(f"Sending message to topic '{topic}' with headers: {headers}")

        self.logger.debug(f"Attempting to send message to topic '{topic}'")

        # Ensure the producer is started before sending
        if not self.producer:
            self.logger.error(
                f"Cannot send message to topic '{topic}': Producer not started."
            )
            # Depending on requirements, you might raise an exception here
            # raise RuntimeError("Producer not started")
            return

        try:
            await self.producer.send(
                topic,
                message,
                headers=headers,
            )
            # Consider logging message content carefully (potential PII/size issues)
            self.logger.debug(f"Message sent to topic '{topic}'")
            # Or just: self.logger.info(f"Message sent to topic '{topic}'")
        except Exception as e:
            self.logger.error(f"Error sending message to topic '{topic}': {str(e)}")

    def decode_headers(self, headers_list: List[tuple[bytes, bytes]]) -> Dict[str, str]:
        # This method doesn't interact with the producer, so it's fine
        decoded_headers: Dict[str, str] = {}
        for key_bytes, value_bytes in headers_list:
            # Ensure robust decoding, handle potential errors
            key = (
                key_bytes.decode("utf-8", errors="replace")
                if isinstance(key_bytes, bytes)
                else str(key_bytes)
            )
            value = (
                value_bytes.decode("utf-8", errors="replace")
                if isinstance(value_bytes, bytes)
                else str(value_bytes)
            )
            decoded_headers[key] = value
        return decoded_headers

    # This function remains the same, it calls the async start_producer
    async def init_kafka_producer(self) -> None:
        await self.start_producer()


# Instantiate the wrapper class (this is fine now as __init__ is lightweight)
kafka_producer = KafkaProducer()
