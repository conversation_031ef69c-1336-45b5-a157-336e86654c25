from __future__ import annotations

import json
import logging
import os
import sys
from typing import Optional

from autogen_agentchat.agents import AssistantAgent
from autogen_core.models import ChatCompletionClient

from ...model_factory import ModelFactory

sys.path.append(os.path.join(os.path.dirname(__file__), "../../.."))
from app.shared.config.base import Settings, get_settings

logger = logging.getLogger(__name__)

# Disable autogen logging for cleaner output
for logger_name in [
    "autogen_agentchat",
    "autogen_core",
    "_single_threaded_agent_runtime",
    "autogen_runtime_core",
    "autogen_agentchat.teams",
    "autogen_agentchat.agents",
]:
    logger_to_silence = logging.getLogger(logger_name)
    logger_to_silence.setLevel(logging.CRITICAL)
    logger_to_silence.propagate = False


class DiscoveryMasterAgent:
    """
    Discovery Master Agent
    This agent is responsible for discovering and scoring specialized agents based on query analysis
    from the registry.
    It uses a curated registry of agents to find the best matches for user tasks."""

    def __init__(self):
        """Initialize the DiscoveryMasterAgent with default configuration."""
        self._settings: Settings = get_settings()
        self._model_client: Optional[ChatCompletionClient] = None
        self._agent: Optional[AssistantAgent] = None
        self._is_initialized: bool = False

    async def initialize(self) -> bool:
        """
        Initialize the Discovery Master Agent.
        """
        try:
            await self._load_agents_registry()
            # Create model client using ModelFactory
            model_config = {
                "provider": "OpenAIChatCompletionClient",
                "llm_type": "openai",
                "model": "gpt-4.1",
                "api_key": self._settings.requesty.api_key,
                "base_url": self._settings.requesty.base_url,
                "model_info": {
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "structured_output": True,
                },
            }

            self._model_client = ModelFactory.create_model_client(model_config)
            if not self._model_client:
                logger.error("Failed to create model client for Discovery Master Agent")
                return False

            self._agent = AssistantAgent(
                name="discovery_agent_master",
                description="Discovers and scores specialized agents based on query analysis from the registry",
                model_client=self._model_client,
                tools=[],
                reflect_on_tool_use=False,
                system_message=self._get_enhanced_system_message(),
            )

            self._is_initialized = True
            logger.info("Discovery Master Agent initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize Discovery Master Agent : {str(e)}")
            self._is_initialized = False
            return False

    def _get_enhanced_system_message(self) -> str:
        """Get the enhanced system message for the discovery agent."""

        registry_text = self._format_agents_for_discovery()

        return f"""You are a Discovery Agent specialized in finding the most suitable specialized agents for user tasks from a curated registry.

                AGENTS REGISTRY:
                {registry_text}

                Your role in the conversation flow:
                1. Wait for query analysis from the Query Analysis Agent
                2. Analyze the task requirements and search through the agents registry 
                3. Score and rank agents based on relevance to the task
                4. Provide discovered agents with scores and reasoning

                When you receive task analysis, respond in this EXACT format:

                DISCOVERY_RESULTS:
                1. AGENT_ID: [agent_id] | SCORE: [0.0-1.0] | REASON: [detailed reasoning]
                2. AGENT_ID: [agent_id] | SCORE: [0.0-1.0] | REASON: [detailed reasoning]  
                3. AGENT_ID: [agent_id] | SCORE: [0.0-1.0] | REASON: [detailed reasoning]

                SUMMARY: [brief summary of discovery results]

                Score agents based on:
                - Specialization match with task domain
                - Expertise alignment with requirements  
                - Keyword overlap with task description
                - Tools/workflows relevance

                Only suggest agents that exist in the registry above. Provide 1-5 top matches ranked by relevance.
            """

    async def _load_agents_registry(self) -> None:
        """Load the specialized agents registry from JSON file."""
        try:
            registry_path = os.path.join(
                os.path.dirname(__file__), "agents_registry.json"
            )

            with open(registry_path, "r") as file:
                registry_data = json.load(file)
                self.agents_registry = registry_data.get("agents", [])

            logger.info(f"Loaded {len(self.agents_registry)} agents from registry")

        except Exception as e:
            logger.error(f"Failed to load agents registry: {e}")
            self.agents_registry = []

    def _format_agents_for_discovery(self) -> str:
        """Format the agent registry for discovery prompt."""
        formatted_agents = []
        for agent in self.agents_registry:
            agent_info = f"""
                - ID: {agent["id"]} | Name: {agent["name"]}
                Specialization: {agent["specialization"]}
                Expertise: {', '.join(agent["expertise"])}
                Keywords: {', '.join(agent["keywords"])}
            """
            formatted_agents.append(agent_info)

        return "\n".join(formatted_agents)

    def get_agent(self) -> Optional[AssistantAgent]:
        """
        Get the underlying AssistantAgent instance.

        Returns:
            Optional[AssistantAgent]: The agent instance if initialized, None otherwise
        """
        return self._agent if self._is_initialized else None

    def is_initialized(self) -> bool:
        """
        Check if the agent is properly initialized.

        Returns:
            bool: True if initialized, False otherwise
        """
        return self._is_initialized

    @classmethod
    async def create_and_initialize(cls) -> Optional["DiscoveryMasterAgent"]:
        """
        Convenience method to create and initialize the Selector system in one call.

        Args:
            model_name: The LLM model to use (uses DISCOVERY_LLM_MODEL env var if not provided)
            api_key: API key

        Returns:
            DiscoveryMasterAgent: Initialized Selector system
        """
        agent = cls()
        if await agent.initialize():
            return agent
        else:
            logger.error("Failed to create and initialize DiscoveryMasterAgent")
            return None
